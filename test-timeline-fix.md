# Timeline Playhead Drag Fix

## 问题描述
反复来回拖拽时间指示器和出入点指示器会造成 "Maximum update depth exceeded" 错误，这是由于无限循环导致的。

## 根本原因
1. `handlePlayheadDragStart` 的 `useCallback` 依赖项中包含了 `currentTime`
2. 在拖拽过程中，`handleMouseMoveGlobal` 调用 `seekToTime(constrainedValue)` 更新了 `currentTime`
3. `currentTime` 的更新导致 `handlePlayheadDragStart` 重新创建
4. 重新创建导致新的事件监听器被添加，而旧的监听器可能没有被正确移除
5. 这导致了多个事件监听器同时存在，造成无限循环

## 修复方案
1. **移除实时的 `seekToTime` 调用**：在拖拽过程中不再调用 `seekToTime`，只更新 `liveCurrentTime` 用于UI显示
2. **移除 `currentTime` 依赖**：从 `useCallback` 的依赖项中移除 `currentTime`，避免因为时间更新导致函数重新创建
3. **只在拖拽结束时同步**：只在 `handleMouseUpGlobal` 中调用 `seekToTime` 来最终同步时间状态

## 修改内容

### 1. 移除拖拽过程中的实时 seekToTime 调用
```typescript
// 修改前
setLiveCurrentTime(constrainedValue);
seekToTime(constrainedValue); // 这里导致无限循环

// 修改后  
setLiveCurrentTime(constrainedValue);
// 移除实时更新全局时间状态，避免无限循环
// seekToTime(constrainedValue);
```

### 2. 移除 currentTime 依赖
```typescript
// 修改前
[
  setLiveCurrentTime,
  setIsDragging,
  setDragType,
  duration,
  zoomLevel,
  currentTime, // 这个依赖导致函数重新创建
  handleAutoScroll,
  stopAutoScroll,
]

// 修改后
[
  setLiveCurrentTime,
  setIsDragging,
  setDragType,
  duration,
  zoomLevel,
  // 移除 currentTime 依赖，避免无限循环
  handleAutoScroll,
  stopAutoScroll,
]
```

### 3. 保持拖拽结束时的同步
```typescript
const handleMouseUpGlobal = () => {
  // 停止自动滚动
  stopAutoScroll();

  // 获取最终的拖拽值
  const finalValue = finalDragValueRef.current ?? currentTime;

  // 只在拖拽结束时更新全局时间状态
  seekToTime(finalValue);

  // 清理实时状态
  setLiveCurrentTime(null);
  
  // ... 其他清理代码
};
```

## 测试验证
修复后应该能够：
1. 正常拖拽播放头而不会出现无限循环错误
2. 拖拽过程中UI实时更新（通过 liveCurrentTime）
3. 拖拽结束后正确同步到视频播放器
4. 反复快速拖拽不会导致崩溃

## 第二次修复：解决播放头位置异常问题

### 问题描述
红色指示线（播放头）第二次拖拽时位置会回到上面，没有在原位置。

### 根本原因
移除 `currentTime` 依赖后，`handlePlayheadDragStart` 函数中的 `currentTime` 变成了过时的值（闭包问题），导致：
1. `dragStartRef.current.originalValue` 使用了过时的 `currentTime`
2. 第二次拖拽时起始位置计算错误

### 修复方案
使用 `useTimelineStore.getState()` 获取最新的 `currentTime` 值：

```typescript
// 修改前
dragStartRef.current = {
  y: e.clientY,
  originalValue: currentTime, // 过时的值
};
setLiveCurrentTime(currentTime); // 过时的值

// 修改后
// 获取最新的 currentTime 值，避免闭包问题
const { currentTime: latestCurrentTime } = useTimelineStore.getState();

dragStartRef.current = {
  y: e.clientY,
  originalValue: latestCurrentTime, // 最新的值
};
setLiveCurrentTime(latestCurrentTime); // 最新的值
```

同样在 `handleMouseUpGlobal` 中：
```typescript
// 修改前
const finalValue = finalDragValueRef.current ?? currentTime; // 过时的值

// 修改后
const { currentTime: latestCurrentTime } = useTimelineStore.getState();
const finalValue = finalDragValueRef.current ?? latestCurrentTime; // 最新的值
```

## 注意事项
- 此修复保持了原有的功能，只是改变了状态更新的时机
- UI响应性得到保持，因为 `liveCurrentTime` 仍然实时更新
- 视频播放器同步在拖拽结束时进行，这实际上是更好的用户体验
- 通过 `useTimelineStore.getState()` 确保始终获取最新的状态值，避免闭包问题
