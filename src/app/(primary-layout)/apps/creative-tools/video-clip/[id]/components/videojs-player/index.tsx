import React, { useRef, useEffect, useState, useMemo } from "react";
import { createPortal } from "react-dom";
// @ts-ignore
import videojs from "video.js";
import "video.js/dist/video-js.css";

interface Caption {
  startTime: number;
  endTime: number;
  text: string;
  translationText: string;
  speaker: string;
}

interface IProps {
  videos: {
    src: string;
    duration: number;
    endTimeSeconds: number;
    startTimeSeconds: number;
    poster?: string;
    captionsList?: Caption[];
  }[];
  // 是否启用按照传入的时间点播放
  useTimeSecond?: boolean;
  // 视频封面图
  poster?: string;
}
export const VideojsPlayer: React.FC<IProps> = (props: IProps) => {
  const { videos, useTimeSecond = false, poster } = props;
  const videoRef = useRef<HTMLVideoElement>(null);
  const playerRef = useRef<any>(null);
  const hideControlsTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const progressBarRef = useRef<HTMLDivElement>(null);
  const progressThumbRef = useRef<HTMLDivElement>(null);
  const currentTimeRef = useRef<HTMLSpanElement>(null);
  // 状态管理
  const [totalDuration, setTotalDuration] = useState("0:36");
  const [isPlaying, setIsPlaying] = useState(false);
  const [volume, setVolume] = useState(100);
  const [isMuted, setIsMuted] = useState(false);
  const [showVolumeSlider, setShowVolumeSlider] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showControls, setShowControls] = useState(true);
  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);
  const [accumulatedTime, setAccumulatedTime] = useState(0);
  const [customControlsContainer, setCustomControlsContainer] =
    useState<HTMLElement | null>(null);
  const [showMoreMenu, setShowMoreMenu] = useState(false);
  const [showSubtitleMenu, setShowSubtitleMenu] = useState(false);
  const [selectedSubtitle, setSelectedSubtitle] = useState("已关闭");
  const [currentSubtitle, setCurrentSubtitle] = useState<string>('');

  //   // 视频配置
  //   const videos = useMemo(() => [
  //     { src: 'http://vjs.zencdn.net/v/oceans.mp4', duration: 36 },
  //     { src: 'https://media.w3.org/2010/05/sintel/trailer.mp4', duration: 52 }
  //   ], []);

  // 计算总时长
  const totalDurationSeconds = useMemo(
    () => videos.reduce((sum, video) => sum + video.duration, 0),
    [videos]
  );

  // 格式化时间显示
  const formatTime = (seconds: number) => {
    // 确保输入是有效的正数
    if (!seconds || seconds < 0 || isNaN(seconds)) {
      return "0:00";
    }
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  // 初始化播放器
  useEffect(() => {
    if (!videoRef.current) return;

    const player = videojs(videoRef.current, {
      controls: false,
      fluid: false,
      responsive: false,
      preload: "metadata",
      playsinline: true,
      poster: poster || videos[0]?.poster, // 使用传入的封面图或第一个视频的封面图
      sources: [
        {
          src: videos[0].src,
          type: "video/mp4",
        },
      ],
    });

    playerRef.current = player;

    // 设置总时长显示
    setTotalDuration(formatTime(totalDurationSeconds));

    // 播放器就绪后的处理
    player.ready(() => {
      // 隐藏原生控制条
      const controlBar = document.querySelector(".vjs-control-bar");
      if (controlBar) {
        (controlBar as HTMLElement).style.display = "none";
      }

      // 创建自定义控制栏容器并添加到播放器中
      const controlsContainer = document.createElement("div");
      controlsContainer.id = "custom-controls-container";
      controlsContainer.style.cssText = `
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 10000;
        pointer-events: none;
      `;

      // 将控制栏容器添加到 video.js 播放器容器中
      const playerContainer = player.el();
      if (playerContainer) {
        playerContainer.appendChild(controlsContainer);
        setCustomControlsContainer(controlsContainer);
      }

      // 如果启用时间段播放，设置起始时间
      if (useTimeSecond && videos[0]?.startTimeSeconds !== undefined) {
        player.currentTime(videos[0].startTimeSeconds);
      }

      // 默认暂停状态，不自动播放
      player.pause();
      setIsPlaying(false);
    });

    // 全屏状态监听
    const handleFullscreenChange = () => {
      const isCurrentlyFullscreen = !!(
        document.fullscreenElement ||
        (document as any).webkitFullscreenElement ||
        (document as any).mozFullScreenElement ||
        (document as any).msFullscreenElement
      );
      setIsFullscreen(isCurrentlyFullscreen);
    };

    // 添加全屏事件监听
    document.addEventListener("fullscreenchange", handleFullscreenChange);
    document.addEventListener("webkitfullscreenchange", handleFullscreenChange);
    document.addEventListener("mozfullscreenchange", handleFullscreenChange);
    document.addEventListener("MSFullscreenChange", handleFullscreenChange);

    return () => {
      if (playerRef.current) {
        playerRef.current.dispose();
        playerRef.current = null;
      }
      // 清理定时器
      if (hideControlsTimeoutRef.current) {
        clearTimeout(hideControlsTimeoutRef.current);
      }

      // 移除全屏事件监听
      document.removeEventListener("fullscreenchange", handleFullscreenChange);
      document.removeEventListener(
        "webkitfullscreenchange",
        handleFullscreenChange
      );
      document.removeEventListener(
        "mozfullscreenchange",
        handleFullscreenChange
      );
      document.removeEventListener(
        "MSFullscreenChange",
        handleFullscreenChange
      );
    };
  }, [videos, totalDurationSeconds, useTimeSecond, poster]);

  // 处理全屏状态变化
  useEffect(() => {
    if (isFullscreen) {
      setShowControls(true);
      // 清除之前的定时器
      if (hideControlsTimeoutRef.current) {
        clearTimeout(hideControlsTimeoutRef.current);
      }
    } else {
      setShowControls(true);
      // 清除定时器
      if (hideControlsTimeoutRef.current) {
        clearTimeout(hideControlsTimeoutRef.current);
      }
    }
  }, [isFullscreen]);

  // 处理进度更新
  useEffect(() => {
    if (!playerRef.current) return;

    const player = playerRef.current;

    const updateProgress = () => {
      const currentVideo = videos[currentVideoIndex];
      let currentTimeSeconds;

      if (useTimeSecond) {
        // 当启用时间段播放时，检查是否超过结束时间
        const actualCurrentTime = player.currentTime();
        if (actualCurrentTime >= currentVideo.endTimeSeconds) {
          // 如果超过结束时间，自动切换到下一个视频
          playNextVideo();
          return;
        }

        // 计算相对于片段开始时间的播放时间
        const segmentCurrentTime = Math.max(
          0,
          actualCurrentTime - currentVideo.startTimeSeconds
        );
        currentTimeSeconds = accumulatedTime + segmentCurrentTime;
      } else {
        currentTimeSeconds = accumulatedTime + player.currentTime();
      }

      // 确保时间和百分比不为负数
      currentTimeSeconds = Math.max(0, currentTimeSeconds);
      const percent = Math.max(
        0,
        Math.min(100, (currentTimeSeconds / totalDurationSeconds) * 100)
      );

      // 直接操作 DOM，避免 React 重新渲染
      if (progressBarRef.current) {
        progressBarRef.current.style.width = `${percent}%`;
      }
      if (progressThumbRef.current) {
        progressThumbRef.current.style.left = `${percent}%`;
      }
      if (currentTimeRef.current) {
        currentTimeRef.current.textContent = formatTime(currentTimeSeconds);
      }
    };

    // 重置 UI 显示
    const resetUIDisplay = () => {
      if (progressBarRef.current) {
        progressBarRef.current.style.width = "0%";
      }
      if (progressThumbRef.current) {
        progressThumbRef.current.style.left = "0%";
      }
      if (currentTimeRef.current) {
        let startTime = "0:00";
        if (
          useTimeSecond &&
          videos[0]?.startTimeSeconds !== undefined &&
          videos[0].startTimeSeconds >= 0
        ) {
          startTime = formatTime(videos[0].startTimeSeconds);
        }
        currentTimeRef.current.textContent = startTime;
      }
    };

    const playNextVideo = () => {
      const currentVideo = videos[currentVideoIndex];
      let segmentDuration;

      if (useTimeSecond) {
        // 使用实际播放的片段时长
        segmentDuration = currentVideo.duration;
      } else {
        segmentDuration = player.currentTime();
      }

      const newAccumulatedTime = accumulatedTime + segmentDuration;
      setAccumulatedTime(newAccumulatedTime);

      const nextIndex = currentVideoIndex + 1;

      if (nextIndex < videos.length) {
        setCurrentVideoIndex(nextIndex);
        const nextVideo = videos[nextIndex];
        player.src(nextVideo.src);

        // 更新封面图
        if (nextVideo.poster) {
          player.poster(nextVideo.poster);
        }

        // 如果启用时间段播放，设置下一个视频的起始时间
        if (useTimeSecond && nextVideo.startTimeSeconds !== undefined) {
          player.one("loadedmetadata", () => {
            player.currentTime(nextVideo.startTimeSeconds);
            player.play();
          });
        } else {
          player.play();
        }
      } else {
        // 所有视频播放完成，重置到开始状态
        player.pause();
        setIsPlaying(false);

        // 重置到第一个视频的开始位置
        setCurrentVideoIndex(0);
        setAccumulatedTime(0);
        const firstVideo = videos[0];
        player.src(firstVideo.src);

        // 重置封面图
        if (poster || firstVideo.poster) {
          player.poster(poster || firstVideo.poster);
        }

        if (useTimeSecond && firstVideo.startTimeSeconds !== undefined) {
          player.one("loadedmetadata", () => {
            player.currentTime(firstVideo.startTimeSeconds);
            // 重置 UI 显示
            resetUIDisplay();
          });
        } else {
          player.one("loadedmetadata", () => {
            player.currentTime(0);
            // 重置 UI 显示
            resetUIDisplay();
          });
        }
      }
    };

    // 事件监听
    player.on("timeupdate", updateProgress);
    player.on("ended", playNextVideo);

    return () => {
      player.off("timeupdate", updateProgress);
      player.off("ended", playNextVideo);
    };
  }, [
    accumulatedTime,
    currentVideoIndex,
    totalDurationSeconds,
    videos,
    useTimeSecond,
    poster,
  ]);





  // 初始化字幕数据
  useEffect(() => {
    const currentVideo = videos[currentVideoIndex];
    const originalCaptions = currentVideo?.captionsList || [];

    // 处理字幕时间映射
    const processCaption = (caption: Caption, video: any) => {
      if (!useTimeSecond) {
        // 普通播放模式，直接使用原始时间
        return caption;
      }

      const sliceStartTime = video.startTimeSeconds;
      const sliceEndTime = video.endTimeSeconds;

      // 判断字幕是否在播放片段范围内
      if (caption.startTime >= sliceStartTime && caption.endTime <= sliceEndTime) {
        // 字幕完全在片段内，重新计算时间
        return {
          ...caption,
          startTime: caption.startTime - sliceStartTime,
          endTime: caption.endTime - sliceStartTime
        };
      } else if (caption.startTime < sliceEndTime && caption.endTime > sliceStartTime) {
        // 字幕与片段有重叠，需要裁剪
        const newStartTime = Math.max(0, caption.startTime - sliceStartTime);
        const newEndTime = Math.min(sliceEndTime - sliceStartTime, caption.endTime - sliceStartTime);

        return {
          ...caption,
          startTime: newStartTime,
          endTime: newEndTime
        };
      }

      // 字幕不在片段范围内，返回 null
      return null;
    };

    // 获取处理后的字幕数据
    const getProcessedCaptions = () => {
      if (!useTimeSecond) {
        return originalCaptions;
      }

      // 过滤并处理字幕时间
      const processedCaptions = originalCaptions
        .map(caption => processCaption(caption, currentVideo))
        .filter(caption => caption !== null);

      return processedCaptions;
    };

    const processedCaptions = getProcessedCaptions();

  }, [currentVideoIndex, videos, useTimeSecond]);

  // 监听视频时间变化，更新字幕
  useEffect(() => {
    if (!playerRef.current) return;

    const player = playerRef.current;
    const currentVideo = videos[currentVideoIndex];
    const originalCaptions = currentVideo?.captionsList || [];

    // 处理字幕时间映射
    const processCaption = (caption: Caption, video: any) => {
      if (!useTimeSecond) {
        // 普通播放模式，直接使用原始时间
        return caption;
      }

      const sliceStartTime = video.startTimeSeconds;
      const sliceEndTime = video.endTimeSeconds;

      // 判断字幕是否在播放片段范围内
      if (caption.startTime >= sliceStartTime && caption.endTime <= sliceEndTime) {
        // 字幕完全在片段内，重新计算时间
        return {
          ...caption,
          startTime: caption.startTime - sliceStartTime,
          endTime: caption.endTime - sliceStartTime
        };
      } else if (caption.startTime < sliceEndTime && caption.endTime > sliceStartTime) {
        // 字幕与片段有重叠，需要裁剪
        const newStartTime = Math.max(0, caption.startTime - sliceStartTime);
        const newEndTime = Math.min(sliceEndTime - sliceStartTime, caption.endTime - sliceStartTime);

        return {
          ...caption,
          startTime: newStartTime,
          endTime: newEndTime
        };
      }

      // 字幕不在片段范围内，返回 null
      return null;
    };

    // 获取处理后的字幕数据
    const getProcessedCaptions = () => {
      if (!useTimeSecond) {
        return originalCaptions;
      }

      // 过滤并处理字幕时间
      const processedCaptions = originalCaptions
        .map(caption => processCaption(caption, currentVideo))
        .filter(caption => caption !== null);

      return processedCaptions;
    };

    // 根据当前时间查找对应的字幕
    const findCurrentSubtitle = (playerCurrentTime: number) => {
      const processedCaptions = getProcessedCaptions();
      if (!processedCaptions || !processedCaptions.length) return '';

      // 计算用于匹配字幕的时间
      let timeForSubtitle = playerCurrentTime;

      if (useTimeSecond) {
        // 在时间段播放模式下，将播放器时间转换为相对时间
        // 因为字幕已经被映射到从0开始的相对时间
        timeForSubtitle = Math.max(0, playerCurrentTime - currentVideo.startTimeSeconds);
      }

      // 查找当前播放时间对应的字幕
      const caption = processedCaptions.find((cap: Caption) => {
        return timeForSubtitle >= cap.startTime && timeForSubtitle <= cap.endTime;
      });

      if (!caption) return '';

      // 根据选择的字幕类型返回不同内容
      switch (selectedSubtitle) {
        case '英文':
          return caption.text || '';
        case '中文':
          return caption.translationText || '';
        case '双语':
          return `${caption.text || ''}\n${caption.translationText || ''}`;
        default:
          return '';
      }
    };

    const updateSubtitle = () => {
      const playerCurrentTime = player.currentTime();
      if (selectedSubtitle !== '已关闭') {
        const subtitle = findCurrentSubtitle(playerCurrentTime);
        if (subtitle !== currentSubtitle) {
          // 计算用于调试的相对时间
          let relativeTime = playerCurrentTime;
          if (useTimeSecond) {
            relativeTime = Math.max(0, playerCurrentTime - currentVideo.startTimeSeconds);
          }

          setCurrentSubtitle(subtitle);
        }
      } else {
        setCurrentSubtitle('');
      }
    };

    player.on('timeupdate', updateSubtitle);

    return () => {
      player.off('timeupdate', updateSubtitle);
    };
  }, [selectedSubtitle, currentVideoIndex, videos, currentSubtitle, accumulatedTime, useTimeSecond]);

  // 播放/暂停控制
  const handlePlayPause = () => {
    if (!playerRef.current) return;

    if (playerRef.current.paused()) {
      playerRef.current.play();
      setIsPlaying(true);
    } else {
      playerRef.current.pause();
      setIsPlaying(false);
    }
  };

  // 音量控制
  const handleVolumeChange = (newVolume: number) => {
    if (!playerRef.current) return;
    setVolume(newVolume);
    playerRef.current.volume(newVolume / 100);
    setIsMuted(newVolume === 0);
  };

  // 静音切换
  const handleMuteToggle = () => {
    if (!playerRef.current) return;
    if (isMuted) {
      playerRef.current.volume(volume / 100);
      setIsMuted(false);
    } else {
      playerRef.current.volume(0);
      setIsMuted(true);
    }
  };

  // 全屏切换
  const handleFullscreenToggle = () => {
    if (!playerRef.current) return;
    if (isFullscreen) {
      playerRef.current?.exitFullscreen?.();
      setIsFullscreen(false);
    } else {
      playerRef.current.requestFullscreen();
      setIsFullscreen(true);
    }
  };

  // 进度条点击
  const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();

    if (!playerRef.current) return;

    const rect = e.currentTarget.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const percent = Math.max(0, Math.min(1, clickX / rect.width));
    const seekTime = percent * totalDurationSeconds;

    // 计算应该跳转到哪个视频
    let timeAccumulated = 0;
    for (let i = 0; i < videos.length; i++) {
      if (seekTime <= timeAccumulated + videos[i].duration) {
        setCurrentVideoIndex(i);
        setAccumulatedTime(timeAccumulated);
        const targetVideo = videos[i];
        playerRef.current.src(targetVideo.src);

        if (useTimeSecond) {
          // 计算在当前片段内的相对时间
          const segmentRelativeTime = seekTime - timeAccumulated;
          const actualTime = targetVideo.startTimeSeconds + segmentRelativeTime;

          // 确保不超过片段的结束时间
          const clampedTime = Math.min(actualTime, targetVideo.endTimeSeconds);

          playerRef.current.one("loadedmetadata", () => {
            playerRef.current.currentTime(clampedTime);
            if (isPlaying) {
              playerRef.current.play();
            }
          });
        } else {
          playerRef.current.currentTime(seekTime - timeAccumulated);
          if (isPlaying) {
            playerRef.current.play();
          }
        }
        break;
      }
      timeAccumulated += videos[i].duration;
    }
  };

  // 鼠标移动处理
  const handleMouseMove = () => {
    if (isFullscreen) {
      setShowControls(true);

      // 清除之前的定时器
      if (hideControlsTimeoutRef.current) {
        clearTimeout(hideControlsTimeoutRef.current);
      }

      // 设置新的定时器，3秒后隐藏控制栏
      hideControlsTimeoutRef.current = setTimeout(() => {
        // setShowControls(false);
      }, 3000);
    }
  };

  // 处理点击外部关闭菜单
  const handleClickOutside = (e: React.MouseEvent) => {
    if (showMoreMenu || showSubtitleMenu) {
      setShowMoreMenu(false);
      setShowSubtitleMenu(false);
    }
  };

  // 控制栏组件
  const ControlBar = ({
    isFullscreenMode = false,
  }: {
    isFullscreenMode?: boolean;
  }) => (
    <div
      className="absolute bottom-0 left-0 right-0 transition-opacity duration-300 z-[10000]"
      style={{
        background:
          "linear-gradient(to top, rgba(0,0,0,0.9), rgba(0,0,0,0.7), transparent)",
        opacity: isFullscreenMode ? (showControls ? 1 : 0) : 1,
        pointerEvents: "auto",
      }}
    >
      {/* 进度条区域 */}
      <div className="px-4 pt-4 pb-2">
        <div
          className="relative py-2 cursor-pointer group"
          onClick={handleProgressClick}
        >
          <div className="relative h-1 bg-white/30 rounded-full">
            <div
              ref={progressBarRef}
              className="absolute top-0 left-0 h-full bg-white rounded-full"
              style={{ width: "0%" }}
            />
            <div
              ref={progressThumbRef}
              className="absolute top-1/2 w-3 h-3 bg-white rounded-full transition-all duration-200 opacity-0 group-hover:opacity-100 group-hover:w-4 group-hover:h-4"
              style={{
                left: "0%",
                transform: "translateX(-50%) translateY(-50%)",
                boxShadow: "0 0 8px rgba(255,255,255,0.5)",
              }}
            />
          </div>
        </div>
      </div>

      {/* 控制按钮区域 */}
      <div className="flex items-center justify-between px-4 pb-4">
        {/* 左侧：播放按钮和时间 */}
        <div className="flex items-center space-x-3">
          <button
            onClick={handlePlayPause}
            className="w-10 h-10 flex items-center justify-center text-white hover:bg-white/20 rounded-full transition-all duration-200 shrink-0"
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            {isPlaying ? (
              <svg
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="currentColor"
                className="flex-shrink-0"
                style={{ display: "block", margin: "auto" }}
              >
                <rect x="6" y="4" width="4" height="16" />
                <rect x="14" y="4" width="4" height="16" />
              </svg>
            ) : (
              <svg
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="currentColor"
                className="flex-shrink-0"
                style={{ display: "block", margin: "auto" }}
              >
                <path d="M8 5v14l11-7z" />
              </svg>
            )}
          </button>
          <span className="text-white text-sm font-medium">
            <span ref={currentTimeRef}>0:00</span> / {totalDuration}
          </span>
        </div>

        {/* 右侧：音量、全屏、更多选项 */}
        <div className="flex items-center space-x-2">
          {/* 音量控制区域 */}
          <div
            className="relative flex items-center"
            onMouseEnter={() => setShowVolumeSlider(true)}
            onMouseLeave={() => setShowVolumeSlider(false)}
          >
            {/* 音量滑块和按钮的组合背景 */}
            <div
              className={`absolute right-0 top-1/2 transform -translate-y-1/2 bg-black/80 rounded-lg transition-all duration-300 ease-in-out flex items-center ${
                showVolumeSlider
                  ? "opacity-100"
                  : "opacity-0 pointer-events-none"
              }`}
            >
              <div className="p-2 flex items-center">
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={isMuted ? 0 : volume}
                  onChange={(e) => handleVolumeChange(Number(e.target.value))}
                  className="w-20 h-1 bg-white/30 rounded-lg appearance-none cursor-pointer volume-slider"
                  style={{
                    background: `linear-gradient(to right, white 0%, white ${
                      isMuted ? 0 : volume
                    }%, rgba(255,255,255,0.3) ${
                      isMuted ? 0 : volume
                    }%, rgba(255,255,255,0.3) 100%)`,
                  }}
                />
              </div>
              <div className="w-10 h-10 flex items-center justify-center text-white shrink-0">
                {isMuted || volume === 0 ? (
                  <svg
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                    className="flex-shrink-0"
                    style={{ display: "block", margin: "auto" }}
                  >
                    <polygon points="11,5 6,9 2,9 2,15 6,15 11,19" />
                    <line x1="23" y1="9" x2="17" y2="15" />
                    <line x1="17" y1="9" x2="23" y2="15" />
                  </svg>
                ) : (
                  <svg
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                    className="flex-shrink-0"
                    style={{ display: "block", margin: "auto" }}
                  >
                    <polygon points="11,5 6,9 2,9 2,15 6,15 11,19" />
                    <path d="M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07" />
                  </svg>
                )}
              </div>
            </div>

            {/* 音量按钮 (始终可见) */}
            <button
              onClick={handleMuteToggle}
              className="w-10 h-10 flex items-center justify-center text-white hover:bg-white/20 rounded-full transition-all duration-200 relative z-10 shrink-0"
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              {isMuted || volume === 0 ? (
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                  className="flex-shrink-0"
                  style={{ display: "block", margin: "auto" }}
                >
                  <polygon points="11,5 6,9 2,9 2,15 6,15 11,19" />
                  <line x1="23" y1="9" x2="17" y2="15" />
                  <line x1="17" y1="9" x2="23" y2="15" />
                </svg>
              ) : (
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                  className="flex-shrink-0"
                  style={{ display: "block", margin: "auto" }}
                >
                  <polygon points="11,5 6,9 2,9 2,15 6,15 11,19" />
                  <path d="M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07" />
                </svg>
              )}
            </button>
          </div>

          {/* 全屏按钮 */}
          <button
            onClick={handleFullscreenToggle}
            className="w-10 h-10 flex items-center justify-center text-white hover:bg-white/20 rounded-full transition-all duration-200 shrink-0"
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            {isFullscreen ? (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="26"
                height="26"
                viewBox="2 -4 28 40"
                className="flex-shrink-0"
              >
                <path
                  fill="#fff"
                  transform="scale(0.0320625 0.0320625)"
                  d="M682 342h128v84h-212v-212h84v128zM598 810v-212h212v84h-128v128h-84zM342 342v-128h84v212h-212v-84h128zM214 682v-84h212v212h-84v-128h-128z"
                ></path>
              </svg>
            ) : (
              <svg
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="currentColor"
                className="flex-shrink-0"
                style={{ display: "block", margin: "auto" }}
              >
                <path d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z" />
              </svg>
            )}
          </button>

          {/* 更多选项 */}
          <div className="relative">
            <button
              onClick={() => setShowMoreMenu(!showMoreMenu)}
              className="w-10 h-10 flex items-center justify-center text-white hover:bg-white/20 rounded-full transition-all duration-200 shrink-0"
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <svg
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="currentColor"
                className="flex-shrink-0"
                style={{ display: "block", margin: "auto" }}
              >
                <circle cx="12" cy="5" r="2" />
                <circle cx="12" cy="12" r="2" />
                <circle cx="12" cy="19" r="2" />
              </svg>
            </button>

            {/* 更多选项菜单 */}
            {showMoreMenu && (
              <div
                className="absolute bottom-12 right-0 rounded-lg shadow-lg min-w-[200px] py-2 z-50 bg-white/90"
                onClick={(e) => e.stopPropagation()}
              >
                <button
                  onClick={() => {
                    setShowSubtitleMenu(true);
                    setShowMoreMenu(false);
                  }}
                  className="w-full px-4 py-3 text-left !flex items-center space-x-3 hover:bg-gray-100"
                >
                  <svg
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                    className="text-black"
                  >
                    <rect x="2" y="15" width="20" height="2" />
                    <rect x="2" y="19" width="20" height="2" />
                    <rect x="2" y="11" width="20" height="2" />
                  </svg>
                  <span className="flex flex-col">
                    <span className="text-black text-sm">字幕</span>
                    <span className="ml-auto text-gray-600 text-sm">
                      {selectedSubtitle}
                    </span>
                  </span>
                </button>

                {/* <button className="w-full px-4 py-3 text-left text-white hover:bg-white/20 flex items-center space-x-3">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <circle cx="12" cy="12" r="10" />
                    <polygon points="10,8 16,12 10,16" />
                  </svg>
                  <span>播放速度</span>
                  <span className="ml-auto text-gray-400">正常</span>
                </button>

                <button className="w-full px-4 py-3 text-left text-white hover:bg-white/20 flex items-center space-x-3">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <rect x="2" y="3" width="20" height="14" rx="2" ry="2" />
                    <line x1="8" y1="21" x2="16" y2="21" />
                    <line x1="12" y1="17" x2="12" y2="21" />
                  </svg>
                  <span>画中画</span>
                </button> */}
              </div>
            )}

            {/* 字幕选择菜单 */}
            {showSubtitleMenu && (
              <div
                className="absolute bottom-12 right-0 bg-white/90 rounded-lg shadow-lg min-w-[200px] py-2 z-50"
                onClick={(e) => e.stopPropagation()}
              >
                <div className="!flex items-center px-4 py-3 border-b border-gray-300">
                  <button
                    onClick={() => {
                      setShowSubtitleMenu(false);
                      setShowMoreMenu(true);
                    }}
                    className="!text-black mr-3"
                  >
                    <svg
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="currentColor"
                    >
                      <path d="M15 18l-6-6 6-6" />
                    </svg>
                  </button>
                  <span className="font-medium text-black !text-sm">选项</span>
                </div>

                <div className="py-2">
                  {["已关闭", "英文", "中文", "双语"].map((subtitle) => (
                    <button
                      key={subtitle}
                      onClick={() => {
                        setSelectedSubtitle(subtitle);
                        setShowSubtitleMenu(false);
                        setShowMoreMenu(false);
                      }}
                      className="w-full px-4 py-3 text-left !text-sm !text-black hover:bg-gray-100 !flex items-center justify-between"
                    >
                      <span>{subtitle}</span>
                      {selectedSubtitle === subtitle && (
                        <svg
                          width="20"
                          height="20"
                          viewBox="0 0 24 24"
                          fill="currentColor"
                        >
                          <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" />
                        </svg>
                      )}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div
      className="relative w-full h-full bg-black font-sans text-white"
      onMouseMove={handleMouseMove}
      onClick={handleClickOutside}
    >
      <style
        dangerouslySetInnerHTML={{
          __html: `
          .volume-slider::-webkit-slider-thumb {
            appearance: none;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: white;
            cursor: pointer;
            box-shadow: 0 0 4px rgba(0,0,0,0.3);
          }
          .volume-slider::-moz-range-thumb {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: white;
            cursor: pointer;
            border: none;
            box-shadow: 0 0 4px rgba(0,0,0,0.3);
          }
          /* 全屏模式下的样式 */
          .video-js.vjs-fullscreen {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            z-index: 9998 !important;
          }
          .video-js.vjs-fullscreen .vjs-tech {
            width: 100% !important;
            height: 100% !important;
            object-fit: contain !important;
          }
          /* 确保全屏模式下控制栏可见 */
          .fullscreen-controls {
            position: fixed !important;
            bottom: 0 !important;
            left: 0 !important;
            right: 0 !important;
            z-index: 10000 !important;
            pointer-events: auto !important;
          }
        `,
        }}
      />
      {/* Video.js 播放器 */}
      <video
        ref={videoRef}
        className="video-js w-full h-full object-contain"
        playsInline
      />

      {/* 字幕显示 */}
      {currentSubtitle && selectedSubtitle !== '已关闭' && (
        <div className="absolute bottom-20 left-1/2 transform -translate-x-1/2 z-40">
          <div className="bg-black/80 text-white px-4 py-3 rounded-lg text-center max-w-[100%] mx-auto">
            {selectedSubtitle === '双语' ? (
              <div className="space-y-1">
                <div className="text-lg font-medium leading-relaxed text-white">
                  {currentSubtitle.split('\n')[0]}
                </div>
                <div className="text-base leading-relaxed text-yellow-200">
                  {currentSubtitle.split('\n')[1]}
                </div>
              </div>
            ) : (
              <span className="text-lg font-medium leading-relaxed">
                {currentSubtitle}
              </span>
            )}
          </div>
        </div>
      )}

      {/* 底部控制栏 - 普通模式 */}
      {!isFullscreen && <ControlBar />}

      {/* 底部控制栏 - 全屏模式 */}
      {isFullscreen &&
        customControlsContainer &&
        createPortal(
          <ControlBar isFullscreenMode={true} />,
          customControlsContainer
        )}
    </div>
  );
};
